// Import DB connection from Lambda Layer

// {
//   "table": "users_eventtypes"
// }


const { getConnections } = await import("/opt/nodejs/getConnections.js").then(m => m);

let initialized = false;
let scheduler2Db;

/** Initialize DB connection */
async function setUpConnections(environment) {
  if (!initialized) {
    ({ scheduler2Db } = await getConnections({ scheduler2Db: environment }));
    initialized = true;
    console.log("✅ DB connection initialized");
  }
  return { scheduler2Db };
}

/** Get all columns for a given table */
async function getTableColumns(tableName) {
  console.log(`🔍 Fetching columns for table: ${tableName}`);

  // Query Postgres system catalog
  const rows = await scheduler2Db("information_schema.columns")
    .select("column_name", "data_type", "is_nullable")
    .where("table_name", tableName)
    .orderBy("ordinal_position");

  console.log("📋 Columns:", rows);
  return rows;
}

export const lambdaHandler = async (event) => {
  try {
    console.log("🚀 Lambda triggered");
    console.log("Input event:", event);

    const env = process.env.SF_ENV || "dev";
    await setUpConnections(env);

    const tableName = event?.table || "user_roles"; // default if not passed
    const columns = await getTableColumns(tableName);

    return {
      statusCode: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        table: tableName,
        columnCount: columns.length,
        columns
      })
    };

  } catch (err) {
    console.error("❌ Error:", err);
    return {
      statusCode: 500,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ error: err.message })
    };
  }
};
