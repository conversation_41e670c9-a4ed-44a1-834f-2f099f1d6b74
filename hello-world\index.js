
// Import Salesforce + DB connections from Lambda Layer
const { getConnections } = await import("/opt/nodejs/getConnections.js").then(m => m);

let initialized = false;
let salesforce, scheduler2Db;

/** Initialize connections */
async function setUpConnections(environment) {
  if (!initialized) {
    ({ salesforce, scheduler2Db } = await getConnections({
      salesforce: environment,
      scheduler2Db: environment
    }));
    initialized = true;
    console.log("Connections initialized");
  }
  return { salesforce, scheduler2Db };
}

/** Fetch Barrier__c records */
async function fetchBarriers({ maxRecords = 50 }) {
  const status = "Ready for Subhub AutoCreate";
  const safeStatus = status.replace(/'/g, "\\'");
  
  const soql = `
    SELECT
      Id,
      RecordType.Name,
      Account__r.BillingPostalCode,
      Locations_of_Barrier__c,
      Account__c,
      Account__r.Name
    FROM Barrier__c
    WHERE Remediation_Status__c = '${safeStatus}'
    ORDER BY LastModifiedDate DESC
    LIMIT ${Math.min(maxR<PERSON>ord<PERSON>, 2000)}
  `;

  console.log("SOQL Query:", soql);
  const result = await salesforce.query(soql);

  return result.records.map(r => {
    const account = r.Account__r || {};
    const rawLocation = r.Locations_of_Barrier__c || "";
    const zipcode = account.BillingPostalCode || (rawLocation.match(/\b\d{5}(?:-\d{4})?\b/) || [null])[0];

    return {
      barrierId: r.Id,
      recordType: r.RecordType?.Name || null,
      accountId: r.Account__c,
      accountName: account.Name || null,
      rawLocation,
      zipcode,
    };
  });
}

/** Map record types → event types */
async function getEventTypeForRecordType(recordTypes) {
  const rows = await scheduler2Db("event_types")
    .select("event_type", "name")
    .whereIn("name", recordTypes);

  const map = {};
  rows.forEach(r => map[r.name] = r.event_type);
  return map;
}

/** Map zipcodes → regions */
async function getRegionForZipcodes(zipcodes) {
  const results = await scheduler2Db('region_zipcodes')
    .select('zip_code', 'region')
    .whereIn('zip_code', zipcodes);

  const map = {};
  results.forEach(r => map[r.zip_code] = r.region);
  return map;
}

/** Fetch partners by event type and optional regions */
async function fetchPartners(eventTypes, regions = [], roleId = 6) {
  let query = scheduler2Db('users as u')
    .select(
      'u.oid',
      scheduler2Db.raw("(u.firstname || ' ' || u.lastname) AS partner_name"),
      'u.region',
      'ur.role_id',
      'et.event_type',
      'et.name AS event_name'
    )
    .join('user_roles as ur', 'u.oid', 'ur.oid')
    .join('users_eventtypes as uet', 'u.oid', 'uet.oid')
    .join('event_types as et', 'uet.type_id', 'et.id')
    .where('u.active', true)
    .where('ur.role_id', roleId)
    .whereIn('et.event_type', eventTypes);

  if (regions.length > 0) query.whereIn('u.region', regions);

  const rows = await query;
  return rows;
}

/** Map partners to each barrier */
function mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap) {
  return barriers.map(b => {
    const eventType = eventTypeMap[b.recordType] || null;
    const regionId = regionMap[b.zipcode] || null;

    // Try partners in same region
    let partners = allPartners.filter(p => p.event_type === eventType && p.region === regionId);

    // Fallback: any partner with event type
    if (!partners.length) partners = allPartners.filter(p => p.event_type === eventType);

    return {
      ...b,
      eventType,
      regionId,
      partners,
    };
  });
}



/** Update a Barrier__c record status in Salesforce 
 * 
 * Only Executes when Job has been created in Scheduler 2 DB
*/
async function markBarrierReady(barrierId) {
  try {
    const result = await salesforce.sobject("Barrier__c").update({
      Id: barrierId,
      Remediation_Status__c: "Ready for Subhub AutoCreate"
    });

    if (!result.success) {
      console.error(`Failed to update Barrier__c ${barrierId}`, result.errors);
      throw new Error(`Update failed: ${result.errors.join(", ")}`);
    }

    console.log(`Barrier__c ${barrierId} updated to Ready for Subhub AutoCreate`);
    return result;
  } catch (err) {
    console.error(`Error updating Barrier__c ${barrierId}:`, err);
    throw err;
  }
}



/** Lambda Handler */
export const lambdaHandler = async (event) => {
  try {
    console.log("Lambda triggered:", event);

    const env = process.env.SF_ENV || "dev";
    const maxRecords = Number(process.env.SF_LIMIT || 50);

    await setUpConnections(env);

    let barriers = event?.zipcode && event?.recordType
      ? [{ barrierId: "TEST_BARRIER", recordType: event.recordType, zipcode: event.zipcode }]
      : await fetchBarriers({ maxRecords });

    if (!barriers.length) return { statusCode: 200, body: JSON.stringify({ message: "No barriers found" }) };

    const uniqueZipcodes = [...new Set(barriers.map(b => b.zipcode).filter(Boolean))];
    const uniqueRecordTypes = [...new Set(barriers.map(b => b.recordType).filter(Boolean))];

    const regionMap = await getRegionForZipcodes(uniqueZipcodes);
    const eventTypeMap = await getEventTypeForRecordType(uniqueRecordTypes);

    const eventTypes = [...new Set(Object.values(eventTypeMap).filter(Boolean))];
    const regions = [...new Set(Object.values(regionMap))];

    const allPartners = await fetchPartners(eventTypes, regions);

    const enriched = mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap);

    return {
      statusCode: 200,
      body: JSON.stringify({
        count: enriched.length,
        sample: enriched.slice(0, 5)
      }),
    };

  } catch (err) {
    console.error("Error:", err);
    return { statusCode: 500, body: JSON.stringify({ error: String(err) }) };
  }
};



