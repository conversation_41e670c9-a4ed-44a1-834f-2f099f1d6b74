
// Import Salesforce + DB connections from Lambda Layer
const { getConnections } = await import("/opt/nodejs/getConnections.js").then(m => m);

let initialized = false;
let salesforce, scheduler2Db;

/** Initialize connections */
async function setUpConnections(environment) {
  if (!initialized) {
    ({ salesforce, scheduler2Db } = await getConnections({
      salesforce: environment,
      scheduler2Db: environment
    }));
    initialized = true;
    console.log("Connections initialized");
  }
  return { salesforce, scheduler2Db };
}

/** Fetch Barrier__c records */
async function fetchBarriers({ maxRecords = 50 }) {
  const status = "Ready for Subhub AutoCreate";
  const safeStatus = status.replace(/'/g, "\\'");
  
  const soql = `
    SELECT
      Id,
      RecordType.Name,
      Account__r.BillingPostalCode,
      Locations_of_Barrier__c,
      Account__c,
      Account__r.Name,
      Number_of_Units_Affected__c
    FROM Barrier__c
    WHERE Remediation_Status__c = '${safeStatus}'
    ORDER BY LastModifiedDate DESC
    LIMIT ${Math.min(maxRecords, 2000)}
  `;

  console.log("SOQL Query:", soql);
  const result = await salesforce.query(soql);

  return result.records.map(r => {
    const account = r.Account__r || {};
    const rawLocation = r.Locations_of_Barrier__c || "";
    const zipcode = account.BillingPostalCode || (rawLocation.match(/\b\d{5}(?:-\d{4})?\b/) || [null])[0];

    return {
      barrierId: r.Id,
      recordType: r.RecordType?.Name || null,
      accountId: r.Account__c,
      accountName: account.Name || null,
      rawLocation,
      zipcode,
      numberOfUnits: r.Number_of_Units_Affected__c || 1,
    };
  });
}

/** Map record types → event types */
async function getEventTypeForRecordType(recordTypes) {
  const rows = await scheduler2Db("event_types")
    .select("event_type", "name")
    .whereIn("name", recordTypes);

  const map = {};
  rows.forEach(r => map[r.name] = r.event_type);
  return map;
}

/** Map zipcodes → regions */
async function getRegionForZipcodes(zipcodes) {
  const results = await scheduler2Db('region_zipcodes')
    .select('zip_code', 'region')
    .whereIn('zip_code', zipcodes);

  const map = {};
  results.forEach(r => map[r.zip_code] = r.region);
  return map;
}

/** Fetch partners by event type and optional regions with current job counts */
async function fetchPartners(eventTypes, regions = [], roleId = 6) {
  let query = scheduler2Db('users as u')
    .select(
      'u.oid',
      scheduler2Db.raw("(u.firstname || ' ' || u.lastname) AS partner_name"),
      'u.region',
      'ur.role_id',
      'et.event_type',
      'et.name AS event_name',
      scheduler2Db.raw('COALESCE(job_counts.total_jobs, 0) as total_job_count')
    )
    .join('user_roles as ur', 'u.oid', 'ur.oid')
    .join('users_eventtypes as uet', 'u.oid', 'uet.oid')
    .join('event_types as et', 'uet.type_id', 'et.id')
    .leftJoin(
      scheduler2Db('partner.event_oids as eo')
        .select('eo.oid', scheduler2Db.raw('COUNT(pe.id) as total_jobs'))
        .join('partner.event as pe', 'eo.event_id', 'pe.id')
        .groupBy('eo.oid')
        .as('job_counts'),
      'u.oid', 'job_counts.oid'
    )
    .where('u.active', true)
    .where('ur.role_id', roleId)
    .whereIn('et.event_type', eventTypes)
    .orderBy('u.region')
    .orderBy('total_job_count', 'asc')
    .orderBy('u.oid');

  if (regions.length > 0) query.whereIn('u.region', regions);

  const rows = await query;
  return rows;
}

/** Get next partner using round-robin logic based on job count */
function getNextPartner(partners, eventType, regionId) {
  if (!partners.length) return null;

  // Sort partners by total job count (ascending) for round-robin
  const sortedPartners = [...partners].sort((a, b) => {
    // Primary sort: by job count (fewer jobs first)
    if (a.total_job_count !== b.total_job_count) {
      return a.total_job_count - b.total_job_count;
    }
    // Secondary sort: by oid for consistent tie-breaking
    return a.oid.localeCompare(b.oid);
  });

  // Return partner with least jobs
  return sortedPartners[0];
}

/** Map partners to each barrier and assign using round-robin based on job count */
function mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap) {
  console.log(`Processing ${barriers.length} barriers for partner assignment`);

  return barriers.map(b => {
    const eventType = eventTypeMap[b.recordType] || null;
    const regionId = regionMap[b.zipcode] || null;

    console.log(`Barrier ${b.barrierId}: recordType=${b.recordType} -> eventType=${eventType}, zipcode=${b.zipcode} -> region=${regionId}`);

    // Try partners in same region first
    let availablePartners = allPartners.filter(p =>
      p.event_type === eventType && p.region === regionId
    );

    // Fallback: any partner with matching event type (any region)
    if (!availablePartners.length) {
      availablePartners = allPartners.filter(p => p.event_type === eventType);
      console.log(`No regional partners found for barrier ${b.barrierId}, using any region. Found ${availablePartners.length} partners`);
    } else {
      console.log(`Found ${availablePartners.length} regional partners for barrier ${b.barrierId}`);
    }

    // Assign partner using round-robin (least jobs first)
    const assignedPartner = getNextPartner(availablePartners, eventType, regionId);

    if (assignedPartner) {
      console.log(`Assigned partner ${assignedPartner.partner_name} (${assignedPartner.total_job_count} jobs) to barrier ${b.barrierId}`);

      // Increment job count for next assignment (simulate assignment)
      assignedPartner.total_job_count += 1;
    } else {
      console.warn(`No available partner found for barrier ${b.barrierId} (eventType: ${eventType}, region: ${regionId})`);
    }

    return {
      ...b,
      eventType,
      regionId,
      availablePartners,
      assignedPartner,
    };
  });
}



/** Create a job in partner.event table */
async function createPartnerEvent(barrier, assignedPartner) {
  try {
    const eventData = {
      type: barrier.eventType,
      status: 'Scheduled',
      account_id: barrier.accountId,
      account_name: barrier.accountName,
      num_unit: barrier.numberOfUnits,
      address: barrier.rawLocation,
      sf_ids: JSON.stringify({ barrier_id: barrier.barrierId }),
      scheduled_date: new Date(),
      last_modified: new Date()
    };

    const [eventId] = await scheduler2Db('partner.event')
      .insert(eventData)
      .returning('id');

    console.log(`Created partner event ${eventId} for barrier ${barrier.barrierId}`);
    return eventId;
  } catch (err) {
    console.error(`Error creating partner event for barrier ${barrier.barrierId}:`, err);
    throw err;
  }
}

/** Create partner assignment in partner.event_oids table */
async function assignPartnerToEvent(eventId, partnerOid) {
  try {
    await scheduler2Db('partner.event_oids')
      .insert({
        event_id: eventId,
        oid: partnerOid
      });

    console.log(`Assigned partner ${partnerOid} to event ${eventId}`);
  } catch (err) {
    console.error(`Error assigning partner ${partnerOid} to event ${eventId}:`, err);
    throw err;
  }
}

/** Update a Barrier__c record status in Salesforce
 *
 * Only Executes when Job has been created in Scheduler 2 DB
*/
async function markBarrierSentForInspection(barrierId) {
  try {
    const result = await salesforce.sobject("Barrier__c").update({
      Id: barrierId,
      Remediation_Status__c: "Sent for Inspection"
    });

    if (!result.success) {
      console.error(`Failed to update Barrier__c ${barrierId}`, result.errors);
      throw new Error(`Update failed: ${result.errors.join(", ")}`);
    }

    console.log(`Barrier__c ${barrierId} updated to Sent for Inspection`);
    return result;
  } catch (err) {
    console.error(`Error updating Barrier__c ${barrierId}:`, err);
    throw err;
  }
}



/** Process barriers and create jobs */
async function processBarriers(barriers) {
  const results = [];

  for (const barrier of barriers) {
    try {
      if (!barrier.assignedPartner) {
        console.warn(`No partner available for barrier ${barrier.barrierId}`);
        results.push({ barrierId: barrier.barrierId, status: 'failed', reason: 'No partner available' });
        continue;
      }

      // Create the partner event
      const eventId = await createPartnerEvent(barrier, barrier.assignedPartner);

      // Assign partner to the event
      await assignPartnerToEvent(eventId, barrier.assignedPartner.oid);

      // Update Salesforce barrier status
      await markBarrierSentForInspection(barrier.barrierId);

      results.push({
        barrierId: barrier.barrierId,
        eventId,
        partnerId: barrier.assignedPartner.oid,
        partnerName: barrier.assignedPartner.partner_name,
        status: 'success'
      });

      console.log(`Successfully processed barrier ${barrier.barrierId} -> event ${eventId} -> partner ${barrier.assignedPartner.partner_name}`);

    } catch (err) {
      console.error(`Failed to process barrier ${barrier.barrierId}:`, err);
      results.push({ barrierId: barrier.barrierId, status: 'failed', error: err.message });
    }
  }

  return results;
}

/** Lambda Handler - Testing partner.event creation */
export const lambdaHandler = async (event) => {
  try {
    console.log("Lambda triggered for testing partner.event creation:", event);

    const env = process.env.SF_ENV || "dev";
    await setUpConnections(env);

    // Test data
    const testBarrier = {
      barrierId: "a2dU80000006iRNIAY",
      eventType: "HEA Visit", // You can change this to match your event type
      accountId: "test_account_123",
      accountName: "Test Account",
      numberOfUnits: 2,
      rawLocation: "123 Test Street, Boston, MA 02134"
    };

    const testPartnerOid = "86ddf1f1-90bf-4536-85cc-519f64c7c9e4";

    console.log(`Testing createPartnerEvent with barrier:`, testBarrier);
    console.log(`Testing with partner OID: ${testPartnerOid}`);

    // Create the partner event
    const eventId = await createPartnerEvent(testBarrier, null);
    console.log(`Created partner event with ID: ${eventId}`);

    // Assign partner to the event
    await assignPartnerToEvent(eventId, testPartnerOid);
    console.log(`Assigned partner ${testPartnerOid} to event ${eventId}`);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Successfully created partner event and assignment",
        eventId: eventId,
        barrierId: testBarrier.barrierId,
        partnerOid: testPartnerOid,
        testData: testBarrier
      }),
    };

  } catch (err) {
    console.error("Error:", err);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: String(err),
        message: "Failed to create partner event"
      })
    };
  }
};

/** Lambda Handler - Full automation workflow */
export const lambdaHandler1 = async (event) => {
  try {
    console.log("Lambda triggered:", event);

    const env = process.env.SF_ENV || "dev";
    const maxRecords = Number(process.env.SF_LIMIT || 50);

    await setUpConnections(env);

    let barriers = event?.zipcode && event?.recordType
      ? [{ barrierId: "TEST_BARRIER", recordType: event.recordType, zipcode: event.zipcode, numberOfUnits: 1 }]
      : await fetchBarriers({ maxRecords });

    if (!barriers.length) {
      return { statusCode: 200, body: JSON.stringify({ message: "No barriers found" }) };
    }

    console.log(`Found ${barriers.length} barriers to process`);

    // Extract unique zipcodes and record types from all barriers
    const uniqueZipcodes = [...new Set(barriers.map(b => b.zipcode).filter(Boolean))];
    const uniqueRecordTypes = [...new Set(barriers.map(b => b.recordType).filter(Boolean))];

    console.log(`Unique zipcodes: ${uniqueZipcodes.join(', ')}`);
    console.log(`Unique record types: ${uniqueRecordTypes.join(', ')}`);

    // Map zipcodes to regions and record types to event types
    const regionMap = await getRegionForZipcodes(uniqueZipcodes);
    const eventTypeMap = await getEventTypeForRecordType(uniqueRecordTypes);

    console.log('Region mapping:', regionMap);
    console.log('Event type mapping:', eventTypeMap);

    // Get all unique event types and regions needed
    const eventTypes = [...new Set(Object.values(eventTypeMap).filter(Boolean))];
    const regions = [...new Set(Object.values(regionMap))];

    console.log(`Fetching partners for event types: ${eventTypes.join(', ')} and regions: ${regions.join(', ')}`);

    // Fetch all available partners with their current job counts
    const allPartners = await fetchPartners(eventTypes, regions);

    console.log(`Found ${allPartners.length} available partners`);

    // Map and assign partners to barriers using round-robin
    const enriched = mapPartnersToBarriers(barriers, allPartners, regionMap, eventTypeMap);

    // Process barriers and create jobs
    const processResults = await processBarriers(enriched);

    const successCount = processResults.filter(r => r.status === 'success').length;
    const failureCount = processResults.filter(r => r.status === 'failed').length;

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Processed ${processResults.length} barriers`,
        successCount,
        failureCount,
        results: processResults
      }),
    };

  } catch (err) {
    console.error("Error:", err);
    return { statusCode: 500, body: JSON.stringify({ error: String(err) }) };
  }
};



