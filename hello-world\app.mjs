import mockSalesforceJobs from "./dummyJobs.js";
import mockPartners from "./dummyPartners.js";


// Keep track of round robin assignment
let roundRobinCounter = {};

async function createSubHubJob(job, partner) {
  // For demo, we won’t actually call SubHub API, just simulate success
  console.log(`Creating SubHub job for ${job.id} with ${partner}`);
  return {
    subhubJobId: "SH-" + Math.floor(Math.random() * 10000),
    status: "Sent for Inspection",
  };
}

export const lambdaHandler = async (event) => {
  console.log("Lambda triggered with event:", event);

  const results = [];

  for (const job of mockSalesforceJobs) {
    if (job.remediationStatus !== "Ready for Subhub AutoCreate") {
      console.log(`Skipping job ${job.id} → Not ready`);
      continue;
    }

    // Find partner for zipcode
    const partners = mockPartners[job.zipcode];
    if (!partners || partners.length === 0) {
      console.log(`Skipping job ${job.id} → No partner in region`);
      continue;
    }

    // Round robin assignment
    const count = roundRobinCounter[job.zipcode] || 0;
    const partner = partners[count % partners.length];
    roundRobinCounter[job.zipcode] = count + 1;

    // Create SubHub job
    const subhubResponse = await createSubHubJob(job, partner);

    // Update Salesforce (simulated)
    console.log(
      `Updating Salesforce for ${job.id} with SubHubId ${subhubResponse.subhubJobId}`
    );

    results.push({
      sfJobId: job.id,
      assignedPartner: partner,
      subhubJobId: subhubResponse.subhubJobId,
      status: subhubResponse.status,
    });
  }

  console.log("SubHub Job AutoCreate Simulation Complete", results);
  return {
    statusCode: 200,
    message: "SubHub Job AutoCreate Simulation Complete",
    results,
  };
};
