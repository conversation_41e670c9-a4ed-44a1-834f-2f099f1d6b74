AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  subhub-automation

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment for Salesforce connection

  MaxRecords:
    Type: Number
    Default: 50
    MinValue: 1
    MaxValue: 2000
    Description: Maximum number of barriers to process per run

Globals:
  Function:
    Timeout: 120

Resources:
  SubhubAutomationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: hello-world/
      Handler: index.lambdaHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      Layers:
        - !Ref ConnectionLayer   # attach local connection layer
      Environment:
        Variables:
          SF_ENV: !Ref Environment
          SF_LIMIT: !Ref MaxRecords
      Events:
        DailySchedule:
          Type: Schedule
          Properties:
            Schedule: cron(0 9 * * ? *)  # Run daily at 9 AM UTC
            Description: "Daily Subhub automation trigger"
            Enabled: true

  ConnectionLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: connectionLayer
      ContentUri: layers/connections/
      CompatibleRuntimes:
        - nodejs20.x

Outputs:
  SubhubAutomationFunction:
    Description: "Subhub Automation Lambda Function ARN"
    Value: !GetAtt SubhubAutomationFunction.Arn
  SubhubAutomationFunctionIamRole:
    Description: "Implicit IAM Role created for Subhub Automation function"
    Value: !GetAtt SubhubAutomationFunctionRole.Arn
  DailyScheduleRule:
    Description: "EventBridge rule for daily automation"
    Value: !Ref SubhubAutomationFunctionDailySchedule
