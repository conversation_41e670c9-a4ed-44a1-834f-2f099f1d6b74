# AWSTemplateFormatVersion: '2010-09-09'
# Transform: AWS::Serverless-2016-10-31
# Description: >
#   subhub-automation

# Globals:
#   Function:
#     Timeout: 3

# Resources:
#   HelloWorldFunction:
#     Type: AWS::Serverless::Function
#     Properties:
#       CodeUri: hello-world/
#       Handler: index.lambdaHandler
#       Runtime: nodejs20.x
#       Architectures:
#         - x86_64
    

# Outputs:
#   HelloWorldFunction:
#     Description: "Hello World Lambda Function ARN"
#     Value: !GetAtt HelloWorldFunction.Arn
#   HelloWorldFunctionIamRole:
#     Description: "Implicit IAM Role created for Hello World function"
#     Value: !GetAtt HelloWorldFunctionRole.Arn


AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  subhub-automation

Globals:
  Function:
    Timeout: 120

Resources:
  HelloWorldFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: hello-world/
      Handler: index.lambdaHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      Layers:
        - !Ref ConnectionLayer   # attach local connection layer

  ConnectionLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: connectionLayer
      ContentUri: layers/connections/
      CompatibleRuntimes:
        - nodejs20.x

Outputs:
  HelloWorldFunction:
    Description: "Hello World Lambda Function ARN"
    Value: !GetAtt HelloWorldFunction.Arn
  HelloWorldFunctionIamRole:
    Description: "Implicit IAM Role created for Hello World function"
    Value: !GetAtt HelloWorldFunctionRole.Arn
