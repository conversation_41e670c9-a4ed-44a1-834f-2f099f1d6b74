const Axios = require('axios');
const AWS = require('aws-sdk');

const s3 = new AWS.S3();

// const getCompletionWithTaskId = async (taskId) => {
//   return uplightAxios.get(`/api/audittask/${taskId}/completion/`);
// };

// const getAuditTasks = async ({
//   projectId,
//   startSearchDate,
//   endSearchDate,
// } = {}) => {
//   const params = {};
//   if (projectId) params.project = projectId;
//   // eslint-disable-next-line camelcase
//   if (startSearchDate) params.last_modified__gte = startSearchDate;
//   // eslint-disable-next-line camelcase
//   if (endSearchDate) params.last_modified__lte = endSearchDate;

//   return uplightAxios.get(`/api/audittask`, {
//     params,
//   });
// };

const errorCallback = (error) => {
  if (error) throw error;
};

const getUplightAxiosInstance = async (leadVendor, environment) => {
  const uplightAxios = Axios.create();

  const { Body: connectionDataBlob } = await s3
    .getObject(
      {
        Bucket: 'hwe-authentication',
        Key: 'uplight/uplightConnectionData.json',
      },
      errorCallback
    )
    .promise();

  const connectionData = JSON.parse(connectionDataBlob.toString('ascii'));

  const { baseUrl, authHeader, acceptHeader } = connectionData[leadVendor][
    environment
  ];

  uplightAxios.defaults.headers.Authorization = authHeader;
  uplightAxios.defaults.headers.Accept = acceptHeader;
  uplightAxios.defaults.baseURL = baseUrl;

  return uplightAxios;
};

const getUplightConnection = async (requestedConnection) => {
  const [leadVendor] = Object.keys(requestedConnection);
  // if (Array.isArray(params))
  //   return params.reduce(async (acc, curr) => {
  //     acc = await acc;
  //     const { leadVendor, environment } = curr;
  //     acc[leadVendor] = await getUplightAxiosInstance(leadVendor, environment);
  //     console.log(acc);
  //     return acc;
  //   }, Promise.resolve({}));

  const environment = requestedConnection[leadVendor];
  return getUplightAxiosInstance(leadVendor, environment);
};

module.exports = { getUplightConnection };
