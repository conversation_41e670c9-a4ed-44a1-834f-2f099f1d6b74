# SubHub Automation Framework

Lambda Layers being utilized:
connectionLayer
sendEmailLayer



How it works ?


Lambda Triggered

Initializes Salesforce + DB connections.

Fetches up to 50 barriers from Salesforce.

Extracts unique zipcodes and record types.

Gets regions for zipcodes from DB.

Gets event types for record types from DB.

Fetches partners by event types and regions.

Maps partners to each barrier (region first, fallback event type).

Returns response with barrier count + sample data.


Update



<!-- Partner Event Table -->
Create a Job and Assign it to partner
