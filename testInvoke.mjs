import fs from "fs";
import { lambdaHandler } from "./hello-world/index.js";

async function invoke() {
  try {
    const event = JSON.parse(
      fs.readFileSync("events/event.json", { encoding: "utf-8" })
    );
    const response = await lambda<PERSON>andler(event);
    console.log("Lambda response:", response);
  } catch (err) {
    console.error("Error invoking lambdaHandler:", err);
  }
}

invoke();
