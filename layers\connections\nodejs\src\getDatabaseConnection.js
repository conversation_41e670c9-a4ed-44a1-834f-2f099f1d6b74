const knex = require('knex');
const AWS = require('aws-sdk');

const s3 = new AWS.S3();

const getDbInstanceWithConnectionString = async (connectionString) =>
  knex({
    client: 'pg',
    connection: connectionString,
    searchPath: ['knex', 'public'],
    useNullAsDefault: true,
    pool: {
      min: 0,
      max: 10,
    },
  });

const errorCallback = (error) => {
  if (error) throw error;
};

const getDatabaseConnection = async (requestedDatabase) => {
  const { Body: connectionDataBlob } = await s3
    .getObject(
      {
        Bucket: 'hwe-authentication',
        Key: 'database/databaseConnectionData.json',
      },
      errorCallback
    )
    .promise();

  const connectionData = JSON.parse(connectionDataBlob.toString('ascii'));

  const [dbName] = Object.keys(requestedDatabase);
  if (typeof requestedDatabase[dbName] === 'string')
    return getDbInstanceWithConnectionString(
      connectionData[dbName][requestedDatabase[dbName]]
    );

  return requestedDatabase[dbName].reduce(async (connections, instanceType) => {
    connections = await connections;
    connections[instanceType] = await getDbInstanceWithConnectionString(
      connectionData[dbName][instanceType]
    );
    return connections;
  }, Promise.resolve({}));
};

module.exports = { getDatabaseConnection };
