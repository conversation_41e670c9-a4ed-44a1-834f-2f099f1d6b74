const mockSalesforceJobs = [
    {
    id: "SF-001",
    accountId: "ACC-123",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "K&T",
    unitsAffected: 12,
    zipcode: "01001",
  },
  {
    id: "SF-002",
    accountId: "ACC-456",
    remediationStatus: "Quote Needed",
    jobType: "Mold",
    unitsAffected: 5,
    zipcode: "02461",
  },
  {
    id: "SF-003",
    accountId: "ACC-789",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 8,
    zipcode: "01002",
  },
  {
    id: "SF-004",
    accountId: "ACC-234",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Fire Damage",
    unitsAffected: 3,
    zipcode: "01007",
  },
  {
    id: "SF-005",
    accountId: "ACC-567",
    remediationStatus: "In Progress",
    jobType: "K&T",
    unitsAffected: 15,
    zipcode: "02134",
  },
  {
    id: "SF-006",
    accountId: "ACC-890",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 7,
    zipcode: "02215",
  },
  {
    id: "SF-007",
    accountId: "ACC-345",
    remediationStatus: "Quote Needed",
    jobType: "Asbestos",
    unitsAffected: 22,
    zipcode: "02116",
  },
  {
    id: "SF-008",
    accountId: "ACC-678",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 4,
    zipcode: "02127",
  },
  {
    id: "SF-009",
    accountId: "ACC-901",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "K&T",
    unitsAffected: 18,
    zipcode: "02145",
  },
  {
    id: "SF-010",
    accountId: "ACC-456",
    remediationStatus: "Completed",
    jobType: "Fire Damage",
    unitsAffected: 1,
    zipcode: "02128",
  },
  {
    id: "SF-011",
    accountId: "ACC-789",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 9,
    zipcode: "02109",
  },
  {
    id: "SF-012",
    accountId: "ACC-012",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 6,
    zipcode: "02131",
  },
  {
    id: "SF-013",
    accountId: "ACC-345",
    remediationStatus: "Quote Needed",
    jobType: "K&T",
    unitsAffected: 13,
    zipcode: "02119",
  },
  {
    id: "SF-014",
    accountId: "ACC-678",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Asbestos",
    unitsAffected: 25,
    zipcode: "02114",
  },
  {
    id: "SF-015",
    accountId: "ACC-901",
    remediationStatus: "In Progress",
    jobType: "Fire Damage",
    unitsAffected: 11,
    zipcode: "02122",
  },
  {
    id: "SF-016",
    accountId: "ACC-234",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 2,
    zipcode: "02136",
  },
  {
    id: "SF-017",
    accountId: "ACC-567",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 16,
    zipcode: "02125",
  },
  {
    id: "SF-018",
    accountId: "ACC-890",
    remediationStatus: "Completed",
    jobType: "K&T",
    unitsAffected: 20,
    zipcode: "02113",
  },
  {
    id: "SF-019",
    accountId: "ACC-123",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Fire Damage",
    unitsAffected: 14,
    zipcode: "02132",
  },
  {
    id: "SF-020",
    accountId: "ACC-456",
    remediationStatus: "Quote Needed",
    jobType: "Asbestos",
    unitsAffected: 30,
    zipcode: "02129",
  },
  {
    id: "SF-021",
    accountId: "ACC-789",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 8,
    zipcode: "02110",
  },
  {
    id: "SF-022",
    accountId: "ACC-012",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 5,
    zipcode: "02124",
  },
  {
    id: "SF-023",
    accountId: "ACC-345",
    remediationStatus: "In Progress",
    jobType: "K&T",
    unitsAffected: 17,
    zipcode: "02135",
  },
  {
    id: "SF-024",
    accountId: "ACC-678",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Fire Damage",
    unitsAffected: 7,
    zipcode: "02111",
  },
  {
    id: "SF-025",
    accountId: "ACC-901",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 12,
    zipcode: "02126",
  },
  {
    id: "SF-026",
    accountId: "ACC-234",
    remediationStatus: "Quote Needed",
    jobType: "Asbestos",
    unitsAffected: 28,
    zipcode: "02133",
  },
  {
    id: "SF-027",
    accountId: "ACC-567",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 10,
    zipcode: "02112",
  },
  {
    id: "SF-028",
    accountId: "ACC-890",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "K&T",
    unitsAffected: 19,
    zipcode: "02130",
  },
  {
    id: "SF-029",
    accountId: "ACC-123",
    remediationStatus: "Completed",
    jobType: "Fire Damage",
    unitsAffected: 6,
    zipcode: "02115",
  },
  {
    id: "SF-030",
    accountId: "ACC-456",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 4,
    zipcode: "02137",
  },
  {
    id: "SF-031",
    accountId: "ACC-789",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 21,
    zipcode: "02120",
  },
  {
    id: "SF-032",
    accountId: "ACC-012",
    remediationStatus: "In Progress",
    jobType: "Asbestos",
    unitsAffected: 35,
    zipcode: "02138",
  },
  {
    id: "SF-033",
    accountId: "ACC-345",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "K&T",
    unitsAffected: 9,
    zipcode: "02117",
  },
  {
    id: "SF-034",
    accountId: "ACC-678",
    remediationStatus: "Quote Needed",
    jobType: "Fire Damage",
    unitsAffected: 15,
    zipcode: "02121",
  },
  {
    id: "SF-035",
    accountId: "ACC-901",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 3,
    zipcode: "02140",
  },
  {
    id: "SF-036",
    accountId: "ACC-234",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 13,
    zipcode: "02141",
  },
  {
    id: "SF-037",
    accountId: "ACC-567",
    remediationStatus: "Completed",
    jobType: "K&T",
    unitsAffected: 24,
    zipcode: "02142",
  },
  {
    id: "SF-038",
    accountId: "ACC-890",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Fire Damage",
    unitsAffected: 8,
    zipcode: "02143",
  },
  {
    id: "SF-039",
    accountId: "ACC-123",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Asbestos",
    unitsAffected: 32,
    zipcode: "02144",
  },
  {
    id: "SF-040",
    accountId: "ACC-456",
    remediationStatus: "In Progress",
    jobType: "Mold",
    unitsAffected: 11,
    zipcode: "02146",
  },
  {
    id: "SF-041",
    accountId: "ACC-789",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 7,
    zipcode: "02147",
  },
  {
    id: "SF-042",
    accountId: "ACC-012",
    remediationStatus: "Quote Needed",
    jobType: "K&T",
    unitsAffected: 16,
    zipcode: "02148",
  },
  {
    id: "SF-043",
    accountId: "ACC-345",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Fire Damage",
    unitsAffected: 5,
    zipcode: "02149",
  },
  {
    id: "SF-044",
    accountId: "ACC-678",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 18,
    zipcode: "02150",
  },
  {
    id: "SF-045",
    accountId: "ACC-901",
    remediationStatus: "Completed",
    jobType: "Water Damage",
    unitsAffected: 23,
    zipcode: "02151",
  },
  {
    id: "SF-046",
    accountId: "ACC-234",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Asbestos",
    unitsAffected: 27,
    zipcode: "02152",
  },
  {
    id: "SF-047",
    accountId: "ACC-567",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "K&T",
    unitsAffected: 14,
    zipcode: "02153",
  },
  {
    id: "SF-048",
    accountId: "ACC-890",
    remediationStatus: "In Progress",
    jobType: "Fire Damage",
    unitsAffected: 9,
    zipcode: "02154",
  },
  {
    id: "SF-049",
    accountId: "ACC-123",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Mold",
    unitsAffected: 6,
    zipcode: "02155",
  },
  {
    id: "SF-050",
    accountId: "ACC-456",
    remediationStatus: "Ready for Subhub AutoCreate",
    jobType: "Water Damage",
    unitsAffected: 12,
    zipcode: "02156",
  },
];

export default mockSalesforceJobs;
