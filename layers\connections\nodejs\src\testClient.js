const { getConnections } = require('../getConnections');

const demo = {
  scheduler1Db: 'dev',
  scheduler2Db: 'dev',
  salesforce: 'dev',
  nationalGrid: 'dev',
  eversource: 'dev',
};

const runTest = async () => {
  const connections = await getConnections(demo);

  try {
    const {
      scheduler1Db,
      scheduler2Db,
      salesforce,
      nationalGrid,
      eversource,
    } = connections;
    // console.log('scheduler1', scheduler1Db);
    // console.log('scheduler2', scheduler2Db);
    // console.log('salesforce', salesforce);
    // console.log('uplight', uplight);

    // const {
    //   rows: [db1Test],
    // } = await scheduler1Db.raw(`select * from users where id = 1786364929683490765 limit 1`);
    // console.log("db1Test", db1Test);

    // const {
    //   rows: [db2Test],
    // } = await scheduler2Db.raw(`select * from users limit 1`);
    // console.log("db2Test", db2Test);

    // const { rows: db2ProdTest } = await scheduler2Db.prod.raw(`Select * from users limit 1`);
    // console.log("db2ProdTest", db2ProdTest);
    // const { rows: db2DevTest } = await scheduler2Db.dev.raw(`Select * from users limit 1`);
    // console.log("db2DevTest", db2DevTest);

    // const sfTest = await salesforce.prod
    //   .sobject("Deal__c")
    //   .select("Account__c")
    //   .where(`Id='a0h0y00000OsklhAAB'`);
    // console.log("sfTest", sfTest);

    // const sfTest = await salesforce
    //   .sobject("Deal__c")
    //   .select("Account__c")
    //   .where(`Id='a0h0y00000OsklhAAB'`);
    // console.log("sfTest", sfTest);
    // const uplightTest = await uplight.get(`/api/audittask/17068/completion/`);
    // console.log('uplight completion test', uplightTest);

    console.log({
      scheduler1Db,
      scheduler2Db,
      salesforce,
      nationalGrid,
      eversource,
    });

    // const uplightTest = await uplight.getCompletedAuditTasks({ startSearchDate: moment().subtract(2, 'months').format() });
    // console.log("uplight audit task test", uplightTest.data);
  } catch (error) {
    console.log(error);
  }
};

runTest();
