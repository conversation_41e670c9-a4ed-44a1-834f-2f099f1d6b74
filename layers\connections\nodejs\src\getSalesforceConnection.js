const AWS = require('aws-sdk');
const jsforce = require('jsforce');

const s3 = new AWS.S3();
const getSalesforceInstance = async (environment) => {
  const errorCallback = (error) => {
    if (error) throw error;
  };

  const keys = {
    application: {
      dev: 'sandbox/salesforce-client_secret.json',
      fullsbx: 'sandbox/salesforce-client_secret.json',
      backup: 'backup/salesforce-client_secret.json',
      prod: 'production/salesforce-client_secret.json',
    },
    access: {
      dev: 'sandbox/SBXaccessTokenEncrypted',
      fullsbx: 'sandbox/SBXaccessTokenEncrypted',
      backup: 'backup/SBXaccessTokenEncrypted',
      prod: 'production/PRODaccessTokenEncrypted',
    },
    refresh: {
      dev: 'sandbox/SBXrefreshTokenEncrypted',
      fullsbx: 'sandbox/SBXrefreshTokenEncrypted',
      backup: 'backup/SBXrefreshTokenEncrypted',
      prod: 'production/PRODrefreshTokenEncrypted',
    },
  };

  const sfApplicationParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.application[environment],
  };

  const applicationResponse = await s3
    .getObject(sfApplicationParams, errorCallback)
    .promise();
  // create Oauth2 object
  const appInfo = JSON.parse(applicationResponse.Body.toString());
  const {
    client_secret: clientSecret,
    client_id: clientId,
    loginUrl,
    redirectUri,
    instanceUrl,
  } = appInfo;

  const oauth2 = new jsforce.OAuth2({
    loginUrl,
    clientId,
    clientSecret,
    redirectUri,
  });

  const sfAccessParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.access[environment],
  };
  const accessResponse = await s3
    .getObject(sfAccessParams, errorCallback)
    .promise();
  const accessTokenParams = { CiphertextBlob: accessResponse.Body };

  const sfRefreshParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.refresh[environment],
  };
  const refreshResponse = await s3
    .getObject(sfRefreshParams, errorCallback)
    .promise();
  const refreshTokenParams = { CiphertextBlob: refreshResponse.Body };

  const kms = new AWS.KMS({ region: 'us-east-1' });

  const { Plaintext: decryptedRefresh } = await kms
    .decrypt(refreshTokenParams, errorCallback)
    .promise();
  const { Plaintext: decryptedAccess } = await kms
    .decrypt(accessTokenParams, errorCallback)
    .promise();

  const accessToken = decryptedAccess.toString('ascii');
  const refreshToken = decryptedRefresh.toString('ascii');

  const salesforceConnection = new jsforce.Connection({
    oauth2,
    accessToken,
    refreshToken,
    instanceUrl,
  });

  salesforceConnection.on('error', errorCallback);

  return salesforceConnection;
};

const getSalesforceConnection = async (environment) => {
  if (typeof environment === 'string')
    return getSalesforceInstance(environment);

  /**
   * Some hoop jumping to get async awaits to work with reduce.
   * Since the accumulator is a promise it must be awaited every time
   */
  return environment.reduce(async (connections, instanceType) => {
    connections = await connections;
    connections[instanceType] = await getSalesforceInstance(instanceType);
    return connections;
  }, Promise.resolve({}));
};

module.exports = { getSalesforceConnection };
